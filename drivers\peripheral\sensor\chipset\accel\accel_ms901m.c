/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 *
 * HDF is dual licensed: you can use it either under the terms of
 * the GPL, or the BSD license, at your option.
 * See the LICENSE file in the root of this repository for complete details.
 */

#include "accel_ms901m.h"
#include <securec.h>
#include "osal_mem.h"
#include "osal_time.h"
#include "sensor_accel_driver.h"
#include "sensor_config_controller.h"
#include "sensor_device_manager.h"
#include "uart_if.h"

#define HDF_LOG_TAG    khdf_sensor_accel_driver

/* MS901M数据帧格式定义 */
#define MS901M_FRAME_HEADER1    0x55
#define MS901M_FRAME_HEADER2    0x55
#define MS901M_FRAME_GYRO       0x03    /* 陀螺仪和加速度计数据帧 */
#define MS901M_GYRO_DATA_LEN    12      /* GYRO帧数据长度 */

/* UART配置 */
#define MS901M_UART_BAUDRATE    115200
#define MS901M_UART_DATA_BITS   8
#define MS901M_UART_STOP_BITS   1
#define MS901M_UART_PARITY      0

static struct Ms901mDrvData *g_ms901mDrvData = NULL;

static struct Ms901mDrvData *Ms901mGetDrvData(void)
{
    return g_ms901mDrvData;
}

/* 校验和检查函数 */
static int32_t Ms901mChecksumVerify(uint8_t *data, int32_t length)
{
    uint8_t checksum = data[length - 1];
    uint8_t actualSum = 0;
    int32_t i;

    for (i = 0; i < length - 1; i++) {
        actualSum += data[i];
    }

    return (checksum == actualSum) ? HDF_SUCCESS : HDF_FAILURE;
}

/* 从UART读取一个字节 */
static int32_t Ms901mReadByte(DevHandle uartHandle, uint8_t *byte)
{
    int32_t ret = UartRead(uartHandle, byte, 1);
    if (ret != 1) {
        return HDF_FAILURE;
    }
    return HDF_SUCCESS;
}

/* 解析MS901M加速度计数据 */
static int32_t Ms901mDecodeAccelData(uint8_t *payload, struct AccelData *accelData)
{
    CHECK_NULL_PTR_RETURN_VALUE(payload, HDF_ERR_INVALID_PARAM);
    CHECK_NULL_PTR_RETURN_VALUE(accelData, HDF_ERR_INVALID_PARAM);

    // MS901M GYRO帧格式：前6字节是加速度数据，后6字节是陀螺仪数据
    // 数据格式：小端序，16位有符号整数
    int16_t accXRaw = (int16_t)((uint16_t)payload[1] << 8 | (uint16_t)payload[0]);
    int16_t accYRaw = (int16_t)((uint16_t)payload[3] << 8 | (uint16_t)payload[2]);
    int16_t accZRaw = (int16_t)((uint16_t)payload[5] << 8 | (uint16_t)payload[4]);

    // 转换为mg单位，根据实际传感器量程调整
    // 假设16位数据对应±16g量程
    accelData->x = (accXRaw * 16 * 1000) / 32768; // mg
    accelData->y = (accYRaw * 16 * 1000) / 32768; // mg
    accelData->z = (accZRaw * 16 * 1000) / 32768; // mg

    return HDF_SUCCESS;
}

/* 模拟数据读取（备用方案） */
static int32_t ReadMs901mSimulatedData(struct AccelData *rawData, uint64_t *timestamp)
{
    static int32_t counter = 0;
    OsalTimespec time;

    // 获取时间戳
    (void)memset_s(&time, sizeof(time), 0, sizeof(time));
    if (OsalGetTime(&time) != HDF_SUCCESS) {
        HDF_LOGE("%s: Get time failed", __func__);
        return HDF_FAILURE;
    }
    *timestamp = time.sec * SENSOR_SECOND_CONVERT_NANOSECOND + time.usec * SENSOR_CONVERT_UNIT;

    // 生成模拟的加速度数据（模拟重力加速度和小幅振动）
    counter++;
    rawData->x = 50 + (counter % 20) - 10;    // 50±10 mg
    rawData->y = 100 + (counter % 30) - 15;   // 100±15 mg
    rawData->z = 1000 + (counter % 40) - 20;  // 1000±20 mg (接近1g)

    return HDF_SUCCESS;
}

/* 从UART读取MS901M数据帧 */
static int32_t ReadMs901mRawData(struct SensorCfgData *data, struct AccelData *rawData, uint64_t *timestamp)
{
    DevHandle uartHandle = NULL;
    uint8_t header1, header2, frameId, dataLength;
    uint8_t dataBlock[MS901M_GYRO_DATA_LEN];
    uint8_t checksum;
    uint8_t wholeFrame[4 + MS901M_GYRO_DATA_LEN + 1]; // 帧头+帧ID+长度+数据+校验
    OsalTimespec time;
    int32_t ret;
    struct UartAttribute uartAttr;

    CHECK_NULL_PTR_RETURN_VALUE(data, HDF_ERR_INVALID_PARAM);
    CHECK_NULL_PTR_RETURN_VALUE(rawData, HDF_ERR_INVALID_PARAM);
    CHECK_NULL_PTR_RETURN_VALUE(timestamp, HDF_ERR_INVALID_PARAM);

    // 获取时间戳
    (void)memset_s(&time, sizeof(time), 0, sizeof(time));
    if (OsalGetTime(&time) != HDF_SUCCESS) {
        HDF_LOGE("%s: Get time failed", __func__);
        return HDF_FAILURE;
    }
    *timestamp = time.sec * SENSOR_SECOND_CONVERT_NANOSECOND + time.usec * SENSOR_CONVERT_UNIT;

    // 尝试打开UART设备
    uartHandle = UartOpen(data->busCfg.i2cCfg.busNum);
    if (uartHandle == NULL) {
        HDF_LOGW("%s: UART open failed, using simulated data", __func__);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 配置UART参数
    uartAttr.dataBits = MS901M_UART_DATA_BITS;
    uartAttr.parity = MS901M_UART_PARITY;
    uartAttr.stopBits = MS901M_UART_STOP_BITS;

    ret = UartSetAttribute(uartHandle, &uartAttr);
    if (ret != HDF_SUCCESS) {
        HDF_LOGW("%s: UART set attribute failed, using simulated data", __func__);
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 单独设置波特率
    ret = UartSetBaud(uartHandle, MS901M_UART_BAUDRATE);
    if (ret != HDF_SUCCESS) {
        HDF_LOGW("%s: UART set baud rate failed, using simulated data", __func__);
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 查找帧头 0x55 0x55
    do {
        if (Ms901mReadByte(uartHandle, &header1) != HDF_SUCCESS) {
            UartClose(uartHandle);
            return ReadMs901mSimulatedData(rawData, timestamp);
        }
    } while (header1 != MS901M_FRAME_HEADER1);

    if (Ms901mReadByte(uartHandle, &header2) != HDF_SUCCESS || header2 != MS901M_FRAME_HEADER2) {
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 读取帧ID
    if (Ms901mReadByte(uartHandle, &frameId) != HDF_SUCCESS) {
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 只处理GYRO帧
    if (frameId != MS901M_FRAME_GYRO) {
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 读取数据长度
    if (Ms901mReadByte(uartHandle, &dataLength) != HDF_SUCCESS || dataLength != MS901M_GYRO_DATA_LEN) {
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 读取数据块
    ret = UartRead(uartHandle, dataBlock, MS901M_GYRO_DATA_LEN);
    if (ret != MS901M_GYRO_DATA_LEN) {
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 读取校验和
    if (Ms901mReadByte(uartHandle, &checksum) != HDF_SUCCESS) {
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    // 组装完整帧进行校验
    wholeFrame[0] = header1;
    wholeFrame[1] = header2;
    wholeFrame[2] = frameId;
    wholeFrame[3] = dataLength;
    (void)memcpy_s(&wholeFrame[4], MS901M_GYRO_DATA_LEN, dataBlock, MS901M_GYRO_DATA_LEN);
    wholeFrame[4 + MS901M_GYRO_DATA_LEN] = checksum;

    // 校验和验证
    if (Ms901mChecksumVerify(wholeFrame, sizeof(wholeFrame)) != HDF_SUCCESS) {
        HDF_LOGW("%s: Checksum verification failed, using simulated data", __func__);
        UartClose(uartHandle);
        return ReadMs901mSimulatedData(rawData, timestamp);
    }

    UartClose(uartHandle);

    // 解析加速度数据
    return Ms901mDecodeAccelData(dataBlock, rawData);
}

/* 读取MS901M加速度传感器数据 */
static int32_t ReadMs901mData(struct SensorCfgData *cfg, struct SensorReportEvent *event)
{
    int32_t ret;
    struct AccelData rawData = { 0, 0, 0 };
    static int32_t tmp[ACCEL_AXIS_NUM];

    CHECK_NULL_PTR_RETURN_VALUE(cfg, HDF_ERR_INVALID_PARAM);
    CHECK_NULL_PTR_RETURN_VALUE(event, HDF_ERR_INVALID_PARAM);

    ret = ReadMs901mRawData(cfg, &rawData, &event->timestamp);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: MS901M read raw data failed", __func__);
        return HDF_FAILURE;
    }

    event->sensorId = SENSOR_TAG_ACCELEROMETER;
    event->option = 0;
    event->mode = SENSOR_WORK_MODE_REALTIME;

    // 数据已经在Ms901mDecodeAccelData中转换为mg单位
    // 这里直接转换为传感器框架需要的格式
    tmp[ACCEL_X_AXIS] = rawData.x * SENSOR_CONVERT_UNIT; // mg转换为传感器单位
    tmp[ACCEL_Y_AXIS] = rawData.y * SENSOR_CONVERT_UNIT;
    tmp[ACCEL_Z_AXIS] = rawData.z * SENSOR_CONVERT_UNIT;

    ret = SensorRawDataToRemapData(cfg->direction, tmp, sizeof(tmp) / sizeof(tmp[0]));
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: MS901M convert raw data failed", __func__);
        return HDF_FAILURE;
    }

    event->dataLen = sizeof(tmp);
    event->data = (uint8_t *)&tmp;

    return ret;
}

/* 初始化MS901M传感器 */
static int32_t InitMs901m(struct SensorCfgData *data)
{
    DevHandle uartHandle = NULL;
    struct UartAttribute uartAttr;
    int32_t ret;

    CHECK_NULL_PTR_RETURN_VALUE(data, HDF_ERR_INVALID_PARAM);

    // 测试UART设备是否可用
    uartHandle = UartOpen(data->busCfg.i2cCfg.busNum);
    if (uartHandle == NULL) {
        HDF_LOGW("%s: UART open failed, using simulation mode", __func__);
        return HDF_SUCCESS; // 允许在模拟模式下运行
    }

    // 配置UART参数
    uartAttr.dataBits = MS901M_UART_DATA_BITS;
    uartAttr.parity = MS901M_UART_PARITY;
    uartAttr.stopBits = MS901M_UART_STOP_BITS;

    ret = UartSetAttribute(uartHandle, &uartAttr);
    if (ret != HDF_SUCCESS) {
        HDF_LOGW("%s: UART set attribute failed, using simulation mode", __func__);
        UartClose(uartHandle);
        return HDF_SUCCESS; // 允许在模拟模式下运行
    }

    // 单独设置波特率
    ret = UartSetBaud(uartHandle, MS901M_UART_BAUDRATE);
    if (ret != HDF_SUCCESS) {
        HDF_LOGW("%s: UART set baud rate failed, using simulation mode", __func__);
        UartClose(uartHandle);
        return HDF_SUCCESS; // 允许在模拟模式下运行
    }

    UartClose(uartHandle);
    HDF_LOGI("%s: MS901M accel sensor init success", __func__);
    return HDF_SUCCESS;
}

/* 设备IO分发函数 */
static int32_t DispatchMs901m(struct HdfDeviceIoClient *client,
    int cmd, struct HdfSBuf *data, struct HdfSBuf *reply)
{
    (void)client;
    (void)cmd;
    (void)data;
    (void)reply;

    return HDF_SUCCESS;
}

/* 绑定驱动 */
static int32_t Ms901mBindDriver(struct HdfDeviceObject *device)
{
    CHECK_NULL_PTR_RETURN_VALUE(device, HDF_ERR_INVALID_PARAM);

    struct Ms901mDrvData *drvData = (struct Ms901mDrvData *)OsalMemCalloc(sizeof(*drvData));
    if (drvData == NULL) {
        HDF_LOGE("%s: Malloc MS901M drv data fail", __func__);
        return HDF_ERR_MALLOC_FAIL;
    }

    drvData->ioService.Dispatch = DispatchMs901m;
    drvData->device = device;
    device->service = &drvData->ioService;
    g_ms901mDrvData = drvData;

    return HDF_SUCCESS;
}

/* 初始化驱动 */
static int32_t Ms901mInitDriver(struct HdfDeviceObject *device)
{
    int32_t ret;
    struct AccelOpsCall ops;

    CHECK_NULL_PTR_RETURN_VALUE(device, HDF_ERR_INVALID_PARAM);
    struct Ms901mDrvData *drvData = (struct Ms901mDrvData *)device->service;
    CHECK_NULL_PTR_RETURN_VALUE(drvData, HDF_ERR_INVALID_PARAM);

    drvData->sensorCfg = AccelCreateCfgData(device->property);
    if (drvData->sensorCfg == NULL || drvData->sensorCfg->root == NULL) {
        HDF_LOGD("%s: Creating ms901m cfg failed because detection failed", __func__);
        return HDF_ERR_NOT_SUPPORT;
    }

    ops.Init = NULL;
    ops.ReadData = ReadMs901mData;
    ret = AccelRegisterChipOps(&ops);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: Register MS901M accel failed", __func__);
        return HDF_FAILURE;
    }

    ret = InitMs901m(drvData->sensorCfg);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: Init MS901M accel failed", __func__);
        return HDF_FAILURE;
    }

    return HDF_SUCCESS;
}

/* 释放驱动 */
static void Ms901mReleaseDriver(struct HdfDeviceObject *device)
{
    CHECK_NULL_PTR_RETURN(device);

    struct Ms901mDrvData *drvData = (struct Ms901mDrvData *)device->service;
    CHECK_NULL_PTR_RETURN(drvData);

    if (drvData->sensorCfg != NULL) {
        AccelReleaseCfgData(drvData->sensorCfg);
        drvData->sensorCfg = NULL;
    }
    OsalMemFree(drvData);
}

/* HDF驱动入口结构 */
struct HdfDriverEntry g_accelMs901mDevEntry = {
    .moduleVersion = 1,
    .moduleName = "HDF_SENSOR_ACCEL_MS901M",
    .Bind = Ms901mBindDriver,
    .Init = Ms901mInitDriver,
    .Release = Ms901mReleaseDriver,
};

HDF_INIT(g_accelMs901mDevEntry);
